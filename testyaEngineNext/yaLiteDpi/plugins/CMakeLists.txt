cmake_minimum_required(VERSION 3.14)

find_package(yaEngineNext REQUIRED)

# ASN.1 协议支持 (仅在启用时包含)
if(ENABLE_ASN1_PROTOCOLS)
    message(STATUS "Setting up ASN.1 protocols...")
    # Include protocol configuration and setup ASN.1 protocols
    include(${CMAKE_CURRENT_SOURCE_DIR}/asn1/protocols.cmake)
    setup_asn1_protocols()
else()
    message(STATUS "ASN.1 protocols are disabled, skipping ASN.1 setup")
endif()

#
# plugins
#
addEngineNextPlugin(udp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_udp.c
)

addEngineNextPlugin(rtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rtp.c
)

addEngineNextPlugin(dns ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_dns.c
)

addEngineNextPlugin(sdx ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_sdx.c
)
addEngineNextPlugin(vlan ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_vlan.c
)

addEngineNextPlugin(llc ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_llc.c

)
addEngineNextPlugin(cdp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_cdp.c
)

addEngineNextPlugin(stp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_stp.c
)

addEngineNextPlugin(ppp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_ppp.c
)

addEngineNextPlugin(gre ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_ipv6.c
)

addEngineNextPlugin(ipv6cp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_ipv6cp.c
)

addEngineNextPlugin(isis ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_isis.c
)

addEngineNextPlugin(ipcp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_ipcp.c
)

addEngineNextPlugin(ipx ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_ipx.c
)

addEngineNextPlugin(lcp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_lcp.c
)

addEngineNextPlugin(xna ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_xna.c
)

addEngineNextPlugin(icmp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_icmp.c
)

addEngineNextPlugin(igmp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_igmp.c
)

addEngineNextPlugin(rsvp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rsvp.c
)

addEngineNextPlugin(gre ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_gre.c
)

addEngineNextPlugin(gtp_u ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_gtp_u.c
)

addEngineNextPlugin(mpls ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_mpls.c
)

addEngineNextPlugin(smtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_smtp.c
  RAGEL_SOURCES parser_smtp.rl
)

addEngineNextPlugin(rt ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_rt.c
)

addEngineNextPlugin(sip ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES       dissector_sip.c
  RAGEL_SOURCES parser_sip.rl
)

addEngineNextPlugin(hwzzeth ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_hwzzeth.c
)

addEngineNextPlugin(trailer_hwzz ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_hwzz.c
)


# H.323 family fixed structure protocols
addEngineNextPlugin(tpkt ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_tpkt.c
)

addEngineNextPlugin(q931 ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_q931.c
)


# Automatically configure plugins for all ASN.1 protocols (仅在启用时)
if(ENABLE_ASN1_PROTOCOLS)
    message(STATUS "Configuring ASN.1 protocol plugins...")
    configure_asn1_plugins()
else()
    message(STATUS "ASN.1 protocols are disabled, skipping ASN.1 plugin configuration")
endif()

# H.225 RAS protocol plugin (separate from h225 CS) - 仅在启用ASN.1时
if(ENABLE_ASN1_PROTOCOLS)
    addEngineNextPlugin(h225ras ${CMAKE_SOURCE_DIR}/bin/plugins
      SOURCES asn1/protocols/h225/dissector_h225ras.c
      LINK_LIBRARIES "-Wl,--whole-archive" asn1_h225 "-Wl,--no-whole-archive"
    )
    add_dependencies(yaNxtDissector_h225ras asn1_build_h225)
    message(STATUS "H.225 RAS plugin configured with ASN.1 support")
else()
    message(STATUS "H.225 RAS plugin skipped (ASN.1 protocols disabled)")
endif()
