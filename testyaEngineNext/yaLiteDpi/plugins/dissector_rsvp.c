#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "rsvp"

// RSVP Message Types (RFC 2205)
#define RSVP_MSG_PATH           1
#define RSVP_MSG_RESV           2
#define RSVP_MSG_PATHERR        3
#define RSVP_MSG_RESVERR        4
#define RSVP_MSG_PATHTEAR       5
#define RSVP_MSG_RESVTEAR       6
#define RSVP_MSG_RESVCONF       7
#define RSVP_MSG_DREQ           8
#define RSVP_MSG_DREP           9
#define RSVP_MSG_RESVTEARCONF   10
#define RSVP_MSG_BUNDLE         12
#define RSVP_MSG_ACK            13
#define RSVP_MSG_SREFRESH       15
#define RSVP_MSG_HELLO          20
#define RSVP_MSG_NOTIFY         21
#define RSVP_MSG_INTEGRITY_CHALLENGE 25
#define RSVP_MSG_INTEGRITY_RESPONSE  26
#define RSVP_MSG_RECOVERYPATH   30

// RSVP Object Classes (RFC 2205)
#define RSVP_CLASS_NULL         0
#define RSVP_CLASS_SESSION      1
#define RSVP_CLASS_RSVP_HOP     3
#define RSVP_CLASS_INTEGRITY    4
#define RSVP_CLASS_TIME_VALUES  5
#define RSVP_CLASS_ERROR_SPEC   6
#define RSVP_CLASS_SCOPE        7
#define RSVP_CLASS_STYLE        8
#define RSVP_CLASS_FLOWSPEC     9
#define RSVP_CLASS_FILTER_SPEC  10
#define RSVP_CLASS_SENDER_TEMPLATE 11
#define RSVP_CLASS_SENDER_TSPEC 12
#define RSVP_CLASS_ADSPEC       13
#define RSVP_CLASS_POLICY_DATA  14
#define RSVP_CLASS_RESV_CONFIRM 15

static const char* rsvp_msg_type_name(uint8_t msg_type)
{
    switch (msg_type) {
        case RSVP_MSG_PATH:           return "Path";
        case RSVP_MSG_RESV:           return "Resv";
        case RSVP_MSG_PATHERR:        return "PathErr";
        case RSVP_MSG_RESVERR:        return "ResvErr";
        case RSVP_MSG_PATHTEAR:       return "PathTear";
        case RSVP_MSG_RESVTEAR:       return "ResvTear";
        case RSVP_MSG_RESVCONF:       return "ResvConf";
        case RSVP_MSG_DREQ:           return "DREQ";
        case RSVP_MSG_DREP:           return "DREP";
        case RSVP_MSG_RESVTEARCONF:   return "ResvTearConfirm";
        case RSVP_MSG_BUNDLE:         return "Bundle";
        case RSVP_MSG_ACK:            return "ACK";
        case RSVP_MSG_SREFRESH:       return "Srefresh";
        case RSVP_MSG_HELLO:          return "Hello";
        case RSVP_MSG_NOTIFY:         return "Notify";
        case RSVP_MSG_INTEGRITY_CHALLENGE: return "Integrity Challenge";
        case RSVP_MSG_INTEGRITY_RESPONSE:  return "Integrity Response";
        case RSVP_MSG_RECOVERYPATH:   return "RecoveryPath";
        default:                      return "Unknown";
    }
}

static const char* rsvp_object_class_name(uint8_t class_num)
{
    switch (class_num) {
        case RSVP_CLASS_NULL:         return "NULL";
        case RSVP_CLASS_SESSION:      return "SESSION";
        case RSVP_CLASS_RSVP_HOP:     return "RSVP_HOP";
        case RSVP_CLASS_INTEGRITY:    return "INTEGRITY";
        case RSVP_CLASS_TIME_VALUES:  return "TIME_VALUES";
        case RSVP_CLASS_ERROR_SPEC:   return "ERROR_SPEC";
        case RSVP_CLASS_SCOPE:        return "SCOPE";
        case RSVP_CLASS_STYLE:        return "STYLE";
        case RSVP_CLASS_FLOWSPEC:     return "FLOWSPEC";
        case RSVP_CLASS_FILTER_SPEC:  return "FILTER_SPEC";
        case RSVP_CLASS_SENDER_TEMPLATE: return "SENDER_TEMPLATE";
        case RSVP_CLASS_SENDER_TSPEC: return "SENDER_TSPEC";
        case RSVP_CLASS_ADSPEC:       return "ADSPEC";
        case RSVP_CLASS_POLICY_DATA:  return "POLICY_DATA";
        case RSVP_CLASS_RESV_CONFIRM: return "RESV_CONFIRM";
        default:                      return "Unknown";
    }
}

// RSVP header structure
typedef struct {
    uint8_t version_flags;  // Version (4 bits) + Flags (4 bits)
    uint8_t msg_type;       // Message Type
    uint16_t checksum;      // Checksum
    uint8_t ttl;            // TTL
    uint8_t reserved;       // Reserved
    uint16_t length;        // Message Length
} rsvp_header_t;

// RSVP object header structure
typedef struct {
    uint16_t length;        // Object Length
    uint8_t class_num;      // Object Class Number
    uint8_t c_type;         // Object C-Type
} rsvp_object_header_t;

// SESSION object for IPv4 (C-Type 1)
typedef struct {
    uint32_t dest_addr;     // Destination Address
    uint8_t protocol_id;    // Protocol ID
    uint8_t flags;          // Flags
    uint16_t dest_port;     // Destination Port
} rsvp_session_ipv4_t;

// RSVP_HOP object for IPv4 (C-Type 1)
typedef struct {
    uint32_t next_hop_addr; // Next/Previous Hop Address
    uint32_t logical_if;    // Logical Interface Handle
} rsvp_hop_ipv4_t;

// TIME_VALUES object (C-Type 1)
typedef struct {
    uint32_t refresh_period; // Refresh Period (ms)
} rsvp_time_values_t;

// ERROR_SPEC object for IPv4 (C-Type 1)
typedef struct {
    uint32_t error_node_addr; // Error Node Address
    uint8_t flags;            // Flags
    uint8_t error_code;       // Error Code
    uint16_t error_value;     // Error Value
} rsvp_error_spec_ipv4_t;

static
int rsvp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
  
  // Check minimum RSVP header length (8 bytes)
  if (nxt_mbuf_get_length(mbuf) < 8) {
    printf("RSVP: insufficient data length (%d bytes, need at least 8)\n", 
      nxt_mbuf_get_length(mbuf));
      return -1;
    }
    
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);
    // Parse RSVP common header
    uint8_t version_flags = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t msg_type = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t reserved _U_ = nxt_mbuf_get_uint8(mbuf, 5);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 6);

    uint8_t version = (version_flags >> 4) & 0x0F;
    uint8_t flags = version_flags & 0x0F;

    // Record RSVP fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "flags", uinteger, flags);
    precord_put(precord, "msg_type", uinteger, msg_type);
    precord_put(precord, "msg_type_name", string, rsvp_msg_type_name(msg_type));
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "ttl", uinteger, ttl);
    precord_put(precord, "length", uinteger, length);

    // Validate message length
    if (length < 8) {
        printf("RSVP: invalid message length %d (minimum 8)\n", length);
        return -1;
    }

    if (nxt_mbuf_get_length(mbuf) < length) {
        printf("RSVP: truncated message (have %d bytes, need %d)\n",
               nxt_mbuf_get_length(mbuf), length);
        return -1;
    }

    // Parse RSVP objects
    uint32_t offset = 8; // Start after common header
    uint32_t object_count = 0;

    while (offset + 4 <= length) {
        // Parse object header
        uint16_t obj_length = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
        uint8_t obj_class = nxt_mbuf_get_uint8(mbuf, offset + 2);
        uint8_t obj_ctype = nxt_mbuf_get_uint8(mbuf, offset + 3);

        if (obj_length < 4) {
            printf("RSVP: invalid object length %d (minimum 4)\n", obj_length);
            break;
        }

        if (offset + obj_length > length) {
            printf("RSVP: object extends beyond message (offset %d + length %d > %d)\n",
                   offset, obj_length, length);
            break;
        }

        // Record object information - use printf for detailed object info
        printf("RSVP Object %d: Class=%d (%s), C-Type=%d, Length=%d\n",
               object_count, obj_class, rsvp_object_class_name(obj_class), obj_ctype, obj_length);

        // Parse specific object types and print detailed information
        if (obj_length >= 8) { // Minimum for most objects
            switch (obj_class) {
                case RSVP_CLASS_SESSION:
                    if (obj_ctype == 1 && obj_length >= 12) { // IPv4 SESSION
                        uint32_t dest_addr = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                        uint8_t protocol_id = nxt_mbuf_get_uint8(mbuf, offset + 8);
                        uint8_t session_flags = nxt_mbuf_get_uint8(mbuf, offset + 9);
                        uint16_t dest_port = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 10);

                        printf("  SESSION: Dest=%d.%d.%d.%d, Protocol=%d, Flags=0x%02x, Port=%d\n",
                               (dest_addr >> 24) & 0xFF, (dest_addr >> 16) & 0xFF,
                               (dest_addr >> 8) & 0xFF, dest_addr & 0xFF,
                               protocol_id, session_flags, dest_port);
                    }
                    break;

                case RSVP_CLASS_RSVP_HOP:
                    if (obj_ctype == 1 && obj_length >= 12) { // IPv4 RSVP_HOP
                        uint32_t next_hop = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                        uint32_t logical_if = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 8);

                        printf("  RSVP_HOP: NextHop=%d.%d.%d.%d, LogicalIF=0x%08x\n",
                               (next_hop >> 24) & 0xFF, (next_hop >> 16) & 0xFF,
                               (next_hop >> 8) & 0xFF, next_hop & 0xFF, logical_if);
                    }
                    break;

                case RSVP_CLASS_TIME_VALUES:
                    if (obj_ctype == 1 && obj_length >= 8) { // TIME_VALUES
                        uint32_t refresh_period = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);

                        printf("  TIME_VALUES: RefreshPeriod=%d ms\n", refresh_period);
                    }
                    break;

                case RSVP_CLASS_ERROR_SPEC:
                    if (obj_ctype == 1 && obj_length >= 12) { // IPv4 ERROR_SPEC
                        uint32_t error_node = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                        uint8_t error_flags = nxt_mbuf_get_uint8(mbuf, offset + 8);
                        uint8_t error_code = nxt_mbuf_get_uint8(mbuf, offset + 9);
                        uint16_t error_value = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 10);

                        printf("  ERROR_SPEC: ErrorNode=%d.%d.%d.%d, Flags=0x%02x, Code=%d, Value=%d\n",
                               (error_node >> 24) & 0xFF, (error_node >> 16) & 0xFF,
                               (error_node >> 8) & 0xFF, error_node & 0xFF,
                               error_flags, error_code, error_value);
                    }
                    break;

                case RSVP_CLASS_STYLE:
                    if (obj_ctype == 1 && obj_length >= 8) { // STYLE
                        uint32_t style_value = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                        printf("  STYLE: Value=0x%08x\n", style_value);
                    }
                    break;

                case RSVP_CLASS_FLOWSPEC:
                    printf("  FLOWSPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                    break;

                case RSVP_CLASS_FILTER_SPEC:
                    printf("  FILTER_SPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                    break;

                case RSVP_CLASS_SENDER_TEMPLATE:
                    printf("  SENDER_TEMPLATE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                    break;

                case RSVP_CLASS_SENDER_TSPEC:
                    printf("  SENDER_TSPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                    break;

                default:
                    printf("  Unknown/Unhandled Object: Class=%d, Length=%d bytes\n", obj_class, obj_length - 4);
                    break;
            }
        }

        // Move to next object
        offset += obj_length;
        object_count++;

        // Prevent infinite loop with too many objects
        if (object_count >= 50) {
            printf("RSVP: too many objects (>= 50), stopping parsing\n");
            break;
        }
    }

    precord_put(precord, "object_count", uinteger, object_count);

    printf("RSVP: Version=%d, Type=%s, TTL=%d, Length=%d, Objects=%d\n",
           version, rsvp_msg_type_name(msg_type), ttl, length, object_count);
    // nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return length; // Return full message length
}

static
int rsvp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "resource reservation protocol");
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field_ex(pschema, "flags", YA_FT_UINT8, "header flags", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "msg_type", YA_FT_UINT8, "message type", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "msg_type_name", YA_FT_STRING, "message type name");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "header checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    pschema_register_field(pschema, "object_count", YA_FT_UINT32, "number of objects");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rsvp",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = rsvp_schema_reg,
    .dissectFun   = rsvp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("ipv4", 46),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rsvp)
{
    nxt_dissector_register(&gDissectorDef);
}
