
        case RSVP_CLASS_SESSION:
            dissect_rsvp_session(pinfo, ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type, rsvph);
            break;

        case RSVP_CLASS_HOP:
            dissect_rsvp_hop(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_TIME_VALUES:
            dissect_rsvp_time_values(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_ERROR:
            dissect_rsvp_error(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_SCOPE:
            dissect_rsvp_scope(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_STYLE:
            dissect_rsvp_style(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_CONFIRM:
            dissect_rsvp_confirm(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_SENDER_TEMPLATE:
        case RSVP_CLASS_FILTER_SPEC:
            dissect_rsvp_template_filter(pinfo, ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type, rsvph);
            break;

        case RSVP_CLASS_SENDER_TSPEC:
            dissect_rsvp_tspec(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_FLOWSPEC:
            dissect_rsvp_flowspec(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_ADSPEC:
            dissect_rsvp_adspec(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_INTEGRITY:
            have_integrity_object = true;
            dissect_rsvp_integrity(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_POLICY:
            dissect_rsvp_policy(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_LABEL_REQUEST:
            dissect_rsvp_label_request(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_RECOVERY_LABEL:
        case RSVP_CLASS_UPSTREAM_LABEL:
        case RSVP_CLASS_SUGGESTED_LABEL:
        case RSVP_CLASS_LABEL:
            dissect_rsvp_label(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_LABEL_SET:
            dissect_rsvp_label_set(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_SESSION_ATTRIBUTE:
            dissect_rsvp_session_attribute(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_EXPLICIT_ROUTE:
            dissect_rsvp_explicit_route(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_RECORD_ROUTE:
            dissect_rsvp_record_route(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_EXCLUDE_ROUTE:
            dissect_rsvp_exclude_route(ti, pinfo, rsvp_object_tree, tvb, offset,
                                       obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_MESSAGE_ID:
            dissect_rsvp_message_id(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_MESSAGE_ID_ACK:
            dissect_rsvp_message_id_ack(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_MESSAGE_ID_LIST:
            dissect_rsvp_message_id_list(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_HELLO:
            dissect_rsvp_hello(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_DCLASS:
            dissect_rsvp_dclass(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_ADMIN_STATUS:
            dissect_rsvp_admin_status(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_LSP_ATTRIBUTES:
        case RSVP_CLASS_LSP_REQUIRED_ATTRIBUTES:
            dissect_rsvp_lsp_attributes(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_ASSOCIATION:
            dissect_rsvp_association(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_LSP_TUNNEL_IF_ID:
            dissect_rsvp_lsp_tunnel_if_id(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_NOTIFY_REQUEST:
            dissect_rsvp_notify_request(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_GENERALIZED_UNI:
            dissect_rsvp_gen_uni(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type, rsvph);
            break;

        case RSVP_CLASS_CALL_ID:
            dissect_rsvp_call_id(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_3GPP2_OBJECT:
            dissect_rsvp_3gpp_object(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_RESTART_CAP:
            dissect_rsvp_restart_cap(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_LINK_CAP:
            dissect_rsvp_link_cap(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_CAPABILITY:
            dissect_rsvp_capability(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_PROTECTION:
            dissect_rsvp_protection_info(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_FAST_REROUTE:
            dissect_rsvp_fast_reroute(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_S2L_SUB_LSP:
            dissect_rsvp_s2l_sub_lsp(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_DETOUR:
            dissect_rsvp_detour(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_DIFFSERV:
            dissect_rsvp_diffserv(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_CLASSTYPE:
            dissect_rsvp_diffserv_aware_te(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_VENDOR_PRIVATE_1:
        case RSVP_CLASS_VENDOR_PRIVATE_2:
        case RSVP_CLASS_VENDOR_PRIVATE_3:
        case RSVP_CLASS_VENDOR_PRIVATE_4:
        case RSVP_CLASS_VENDOR_PRIVATE_5:
        case RSVP_CLASS_VENDOR_PRIVATE_6:
        case RSVP_CLASS_VENDOR_PRIVATE_7:
        case RSVP_CLASS_VENDOR_PRIVATE_8:
        case RSVP_CLASS_VENDOR_PRIVATE_9:
        case RSVP_CLASS_VENDOR_PRIVATE_10:
        case RSVP_CLASS_VENDOR_PRIVATE_11:
        case RSVP_CLASS_VENDOR_PRIVATE_12:
            dissect_rsvp_vendor_private_use(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_SECONDARY_EXPLICIT_ROUTE:
            dissect_rsvp_secondary_explicit_route(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;
        case RSVP_CLASS_SECONDARY_RECORD_ROUTE:
            dissect_rsvp_secondary_record_route(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_CALL_ATTRIBUTES:
            dissect_rsvp_call_attributes(ti, pinfo, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;

        case RSVP_CLASS_JUNIPER_PROPERTIES:
            dissect_rsvp_juniper(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;
        case RSVP_CLASS_NULL:
        default:
            dissect_rsvp_unknown(ti, rsvp_object_tree, tvb, offset, obj_length, rsvp_class, type);
            break;
        }
